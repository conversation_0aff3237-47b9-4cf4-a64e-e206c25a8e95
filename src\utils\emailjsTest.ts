import emailjs from '@emailjs/browser';

// EmailJS Configuration Test Utility
export const testEmailJSConfiguration = async () => {
  console.log('=== EmailJS Configuration Test ===');
  
  // Initialize EmailJS
  console.log('Initializing EmailJS with public key: pyjj4z90GoTsyHDQT');
  emailjs.init('pyjj4z90GoTsyHDQT');
  
  // Test data
  const testData = {
    event_plan: 'Test Event - Configuration Check',
    bride_name: 'Test Bride',
    groom_name: 'Test Groom',
    phone_number: '+94123456789',
    user_email: '<EMAIL>',
    submission_time: new Date().toLocaleString(),
    booking_reference: 'TEST-CONFIG-' + Date.now(),
    is_engagement: true,
    is_wedding: false,
    is_homecoming: false,
    is_combo: false,
    is_triple_combo: false,
    engagement_package: 'Test Package 1',
    engagement_date: '2024-01-01',
    venue_hotel_engagement: 'Test Venue',
    registration_time: '10:00',
    ring_exchange_time: '11:00',
    event_end_time_engagement: '18:00',
    guest_count_engagement: '50',
    makeup_artist_engagement: 'Test Makeup Artist',
    additional_notes_engagement: 'This is a test email to verify EmailJS configuration.'
  };

  try {
    console.log('Sending test email...');
    console.log('Service ID: service_ywznx8m');
    console.log('Template ID: template_rae6acc');
    console.log('Test data:', testData);

    const result = await emailjs.send(
      'service_ywznx8m',
      'template_rae6acc',
      testData,
      {
        publicKey: 'pyjj4z90GoTsyHDQT'
      }
    );

    console.log('✅ Test email sent successfully!');
    console.log('Result:', result);
    console.log('Status:', result.status);
    console.log('Text:', result.text);
    
    return {
      success: true,
      result: result,
      message: 'EmailJS configuration is working correctly!'
    };

  } catch (error: any) {
    console.error('❌ Test email failed!');
    console.error('Error:', error);
    console.error('Error message:', error.message);
    console.error('Error status:', error.status);
    console.error('Error text:', error.text);
    
    return {
      success: false,
      error: error,
      message: `EmailJS configuration failed: ${error.text || error.message || 'Unknown error'}`
    };
  }
};

// Validate EmailJS credentials
export const validateEmailJSCredentials = () => {
  const credentials = {
    publicKey: 'pyjj4z90GoTsyHDQT',
    serviceId: 'service_ywznx8m',
    templateId: 'template_rae6acc'
  };

  console.log('=== EmailJS Credentials Validation ===');
  console.log('Public Key:', credentials.publicKey);
  console.log('Service ID:', credentials.serviceId);
  console.log('Template ID:', credentials.templateId);

  // Basic validation
  const validations = {
    publicKeyValid: credentials.publicKey && credentials.publicKey.length > 10,
    serviceIdValid: credentials.serviceId && credentials.serviceId.startsWith('service_'),
    templateIdValid: credentials.templateId && credentials.templateId.startsWith('template_')
  };

  console.log('Validation results:', validations);

  const allValid = Object.values(validations).every(v => v);
  console.log('All credentials valid:', allValid);

  return {
    credentials,
    validations,
    allValid
  };
};
