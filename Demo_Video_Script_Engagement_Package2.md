# Demo Video Script - Engagement Session Package 2
## Professional Photography Booking System Demonstration

**Target Package:** Engagement Session - Package 2 (55,000.00 LKR)  
**Test Email:** <EMAIL>  
**Demo Purpose:** Complete booking workflow demonstration for client presentation  
**Total Duration:** ~4-5 minutes

---

## **SEGMENT 1: Introduction (0:00 - 0:30)**
**Duration:** 30 seconds

### Voice-Over Script:
*"Welcome to the Professional Photography Booking System Demo. Today we'll demonstrate how clients can seamlessly book their engagement photography session using our automated booking platform. This system streamlines the entire process from inquiry to confirmation, saving photographers hours of manual work while providing clients with a professional experience."*

### Visual Actions:
- Show homepage/landing page
- Highlight professional design elements
- Navigate to booking page
- Show clean, modern interface

---

## **SEGMENT 2: Event Type Selection (0:30 - 1:00)**
**Duration:** 30 seconds

### Voice-Over Script:
*"The booking process begins with event type selection. Our system supports multiple photography services including engagement sessions, weddings, homecoming celebrations, and combination packages. For this demonstration, we'll select an Engagement Session."*

### Visual Actions:
- Click on "Engagement Session" option
- Show automatic progression to step 2
- Highlight the smooth transition animation
- Display step progress indicator

---

## **SEGMENT 3: Package Selection & Details (1:00 - 1:45)**
**Duration:** 45 seconds

### Voice-Over Script:
*"Next, clients choose their preferred package. We're selecting Package 2 for 55,000 LKR, which includes main photo session at preferred location, entire ceremony and reception coverage, thank you card design, one enlarged 16x24 photo, 50 thank you cards, 40 high-end edited photos, drone photography, and all unedited photos and videos on flash drive. The system automatically displays all package details for complete transparency."*

### Visual Actions:
- Select "Package 2 - 55,000.00 LKR" from dropdown
- Show package details expanding automatically
- Highlight each included feature with checkmarks
- Demonstrate clear pricing display

---

## **SEGMENT 4: Event Details Form Completion (1:45 - 2:30)**
**Duration:** 45 seconds

### Voice-Over Script:
*"Now we'll complete the event details. The bride's name is Sarah Johnson, groom's name is Michael Chen. We'll set the engagement date for March 15th, 2024, with registration at 2:00 PM and ring exchange at 3:30 PM. The venue is Grand Hotel Colombo, expecting 75 guests, with makeup artist Maria Silva, and the event ending at 8:00 PM."*

### Visual Actions:
- Fill "Bride's Name": Sarah Johnson
- Fill "Groom's Name": Michael Chen  
- Select engagement date: 2024-03-15
- Set registration time: 14:00
- Set ring exchange time: 15:30
- Fill venue: Grand Hotel Colombo
- Set guest count: 75
- Fill makeup artist: Maria Silva
- Set event end time: 20:00
- Click "Next" to proceed

---

## **SEGMENT 5: Contact Information (2:30 - 3:00)**
**Duration:** 30 seconds

### Voice-Over Script:
*"The contact information section captures essential client details. We'll enter the phone number, email address, and how they heard about our services. This information is crucial for follow-up communication and marketing insights."*

### Visual Actions:
- Fill phone number: +***********
- Fill email: <EMAIL>
- Select "Social Media" for "How did you hear about us"
- Show form validation working
- Click "Next" to review

---

## **SEGMENT 6: Review & Terms Agreement (3:00 - 3:30)**
**Duration:** 30 seconds

### Voice-Over Script:
*"The review section displays all entered information for client verification. Clients must agree to our terms and conditions, which include the 10,000 LKR non-refundable advance payment, balance payment one week prior to the event, and other important policies."*

### Visual Actions:
- Show complete booking summary
- Scroll through terms and conditions
- Check the "I agree to terms and conditions" checkbox
- Highlight the three action buttons: WhatsApp, Download PDF, Test Email

---

## **SEGMENT 7: WhatsApp Integration Demo (3:30 - 4:00)**
**Duration:** 30 seconds

### Voice-Over Script:
*"Clicking 'Submit via WhatsApp' triggers our automated system. First, an email is sent to our team with all booking details and calendar invites. Simultaneously, WhatsApp opens with a pre-filled message containing the complete booking information and unique reference number."*

### Visual Actions:
- Click "Submit via WhatsApp" button
- Show loading state
- Display success notification
- Show WhatsApp opening with pre-filled message
- Highlight booking reference number in message

---

## **SEGMENT 8: Email Confirmation Demo (4:00 - 4:30)**
**Duration:** 30 seconds

### Voice-Over Script:
*"Let's check the email confirmation. The system automatically sends a comprehensive <NAME_EMAIL> with all booking details, package information, event timeline, and 'Add to Google Calendar' links. This ensures nothing is missed and provides professional documentation."*

### Visual Actions:
- Open email client/browser
- Show received email in inbox
- Open the booking confirmation email
- Scroll through email content showing:
  - Booking reference
  - Event details
  - Package information
  - Calendar links
  - Contact information

---

## **SEGMENT 9: Calendar Integration Showcase (4:30 - 4:50)**
**Duration:** 20 seconds

### Voice-Over Script:
*"The email includes 'Add to Google Calendar' links for easy scheduling. Clicking these links automatically creates calendar events with all relevant details pre-filled, ensuring both photographer and client stay organized."*

### Visual Actions:
- Click on calendar link in email
- Show Google Calendar opening
- Display event creation with all details pre-filled
- Show event saved in calendar

---

## **SEGMENT 10: Professional Conclusion (4:50 - 5:00)**
**Duration:** 10 seconds

### Voice-Over Script:
*"This completes our booking system demonstration. The entire process takes just minutes, automatically handles email notifications, WhatsApp communication, and 'Add to Calendar' functionality - transforming hours of manual work into an efficient, professional experience."*

### Visual Actions:
- Show final success page
- Display Tera Works branding
- Show contact information: +94 71 576 8552

---

## **TECHNICAL NOTES FOR PRODUCTION:**

### **Timing Considerations:**
- Allow 2-3 seconds buffer between segments for smooth transitions
- Account for realistic typing speed (approximately 40-50 WPM)
- Include brief pauses for form loading/processing
- Allow time for email loading in demonstration

### **Voice-Over Production:**
- Use professional, clear narration
- Maintain consistent pace throughout
- Emphasize key benefits and features
- Include slight pauses at commas and periods

### **Visual Quality:**
- Record in 1080p minimum resolution
- Use clean browser window without bookmarks/extensions
- Ensure consistent mouse movement speed
- Highlight important UI elements with subtle animations

### **System Response Times:**
- EmailJS typically responds within 2-3 seconds
- WhatsApp opening may take 1-2 seconds
- Calendar integration is instant
- Allow buffer time for each action

### **Demo Data Summary:**
- **Bride:** Sarah Johnson
- **Groom:** Michael Chen
- **Date:** March 15, 2024
- **Package:** Package 2 (55,000.00 LKR)
- **Venue:** Grand Hotel Colombo
- **Email:** <EMAIL>
- **Phone:** +***********
- **Guests:** 75
- **Makeup Artist:** Maria Silva

---

**Built by Tera Works - Professional Web Solutions**  
**Contact:** +94 71 576 8552 | WhatsApp Integration Specialist
