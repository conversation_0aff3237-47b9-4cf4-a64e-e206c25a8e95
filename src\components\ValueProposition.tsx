import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Camera, Mail, MessageCircle, Calendar, Clock, Star, Globe } from 'lucide-react';
import { motion } from 'framer-motion';

const ValueProposition = () => {
  const [language, setLanguage] = useState<'en' | 'si'>('en');

  const content = {
    en: {
      title: "Why Choose This Booking System?",
      subtitle: "Streamline your photography business with professional automation",
      features: [
        {
          icon: Mail,
          title: "Email Automation",
          description: "Professional email templates automatically sent to clients with booking details, calendar invites, and confirmation information."
        },
        {
          icon: MessageCircle,
          title: "WhatsApp Integration",
          description: "Instant WhatsApp messaging with pre-filled booking details for immediate client communication and faster response times."
        },
        {
          icon: Calendar,
          title: "Calendar Integration",
          description: "Automatic Google Calendar invites for all events, ensuring both you and your clients never miss important dates."
        },
        {
          icon: Clock,
          title: "Time-Saving Automation",
          description: "Reduce manual work by 80% with automated booking processing, email sending, and client communication workflows."
        },
        {
          icon: Star,
          title: "Professional Presentation",
          description: "Impress clients with a polished, professional booking experience that builds trust and confidence in your services."
        },
        {
          icon: Camera,
          title: "Photography-Focused",
          description: "Specifically designed for photographers with event-specific forms, package management, and industry-relevant features."
        }
      ],
      benefits: {
        title: "Benefits for Photographers",
        items: [
          "Save 5+ hours per week on booking management",
          "Reduce booking errors and miscommunication",
          "Increase client satisfaction with professional experience",
          "Streamline your workflow from inquiry to confirmation",
          "Focus more time on photography, less on administration"
        ]
      },
      cta: "Get This System for Your Business"
    },
    si: {
      title: "මෙම Booking System එක තෝරා ගන්නේ ඇයි?",
      subtitle: "ප්‍රොෆෙෂනල් ස්වයංක්‍රීයකරණයෙන් ඔබේ photography ව්‍යාපාරය වැඩිදියුණු කරන්න",
      features: [
        {
          icon: Mail,
          title: "Email ස්වයංක්‍රීයකරණය",
          description: "Booking විස්තර, calendar invites සහ confirmation තොරතුරු සමඟ ස්වයංක්‍රීයව clients වෙත යවන ප්‍රොෆෙෂනල් email templates."
        },
        {
          icon: MessageCircle,
          title: "WhatsApp Integration",
          description: "Pre-filled booking විස්තර සමඟ ක්ෂණික WhatsApp messaging, client communication සහ වේගවත් response times සඳහා."
        },
        {
          icon: Calendar,
          title: "Calendar Integration",
          description: "සියලුම events සඳහා ස්වයංක්‍රීය Google Calendar invites, ඔබ සහ ඔබේ clients කිසිදු වැදගත් දිනයක් අමතක නොකරන බව සහතික කරයි."
        },
        {
          icon: Clock,
          title: "කාලය ඉතිරි කරන ස්වයංක්‍රීයකරණය",
          description: "Automated booking processing, email sending සහ client communication workflows සමඟ manual වැඩ 80%කින් අඩු කරන්න."
        },
        {
          icon: Star,
          title: "ප්‍රොෆෙෂනල් Presentation",
          description: "ඔබේ සේවාවන් කෙරෙහි විශ්වාසය සහ විශ්වාසනීයත්වය ගොඩනගන polished, professional booking අත්දැකීමකින් clients ව විශ්මයට පත් කරන්න."
        },
        {
          icon: Camera,
          title: "Photography-Focused",
          description: "Event-specific forms, package management සහ industry-relevant features සමඟ photographers සඳහා විශේෂයෙන් නිර්මාණය කර ඇත."
        }
      ],
      benefits: {
        title: "Photographers සඳහා ප්‍රතිලාභ",
        items: [
          "සතියකට පැය 5+ක් booking management වලින් ඉතිරි කරන්න",
          "Booking errors සහ miscommunication අඩු කරන්න",
          "Professional අත්දැකීමකින් client satisfaction වැඩි කරන්න",
          "Inquiry සිට confirmation දක්වා ඔබේ workflow streamline කරන්න",
          "Administration වලට අඩු කාලයක්, photography සඳහා වැඩි කාලයක් focus කරන්න"
        ]
      },
      cta: "ඔබේ ව්‍යාපාරය සඳහා මෙම System එක ගන්න"
    }
  };

  const currentContent = content[language];

  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-16">
      <div className="container mx-auto px-4">
        {/* Language Toggle */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-md">
            <Button
              variant={language === 'en' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setLanguage('en')}
              className="mr-1"
            >
              <Globe className="w-4 h-4 mr-1" />
              English
            </Button>
            <Button
              variant={language === 'si' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setLanguage('si')}
            >
              සිංහල
            </Button>
          </div>
        </div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            {currentContent.title}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {currentContent.subtitle}
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {currentContent.features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="bg-blue-100 p-3 rounded-lg mr-4">
                        <IconComponent className="w-6 h-6 text-blue-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-800">
                        {feature.title}
                      </h3>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Benefits Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-lg p-8 mb-8"
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            {currentContent.benefits.title}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {currentContent.benefits.items.map((benefit, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="bg-green-100 rounded-full p-1 mt-1">
                  <Star className="w-4 h-4 text-green-600" />
                </div>
                <span className="text-gray-700">{benefit}</span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <Button
            onClick={() => window.open('https://wa.me/94715768552', '_blank')}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-lg font-medium text-lg transition-all duration-300 transform hover:scale-105"
          >
            {currentContent.cta}
          </Button>
        </motion.div>
      </div>
    </div>
  );
};

export default ValueProposition;
