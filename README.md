# Professional Photography Booking System Demo

## 🎯 Overview

This is a comprehensive demonstration of a professional photography booking system built specifically for photographers. The system streamlines the entire booking process from initial inquiry to confirmation, featuring automated email integration, WhatsApp messaging, and calendar synchronization.

## ✨ Key Features

### 📧 Email Automation
- Professional email templates with booking details
- Automatic confirmation emails to clients
- Calendar invites for all events
- Responsive email design for all devices

### 📱 WhatsApp Integration
- Instant WhatsApp messaging with pre-filled booking details
- Automated message generation based on event type
- Direct communication channel with clients

### 📅 Calendar Integration
- Automatic Google Calendar invite generation
- Event-specific calendar entries
- Multiple event support for combo packages

### 🎨 Professional UI/UX
- Modern, responsive design
- Multi-step booking form
- Real-time form validation
- Mobile-optimized interface

### 📊 Package Management
- Dynamic package selection based on event type
- Detailed package information display
- Pricing integration
- Custom package configurations

## 🌐 Bilingual Support

The system includes both English and Sinhala language support for value propositions and key messaging, making it accessible to a broader client base.

## 🛠 Technical Stack

- **Frontend**: React 18 with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **Build Tool**: Vite
- **Email Service**: EmailJS
- **PDF Generation**: jsPDF
- **Animations**: Framer Motion
- **Form Handling**: React Hook Form with Zod validation
- **Date Handling**: date-fns
- **Icons**: Lucide React

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Contact Tera Works for system setup:
```
WhatsApp: +94 71 576 8552
Email: <EMAIL>
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## 📋 Configuration

### EmailJS Setup
To enable email functionality, you'll need to:
1. Create an EmailJS account
2. Set up a service and template
3. Update the configuration in `src/pages/Booking.tsx`

### WhatsApp Integration
Update the phone number in `src/utils/whatsapp.ts` to your business WhatsApp number.

## 🎨 Customization

### Branding
- Update colors in `tailwind.config.ts`
- Modify logos and branding text throughout the application
- Customize email templates in `Professional_EmailJS_Template.html`

### Packages
- Edit package information in `Packages.md`
- Update pricing and package details in the booking form

## 📱 Demo Features

This demo showcases:
- ✅ Complete booking workflow
- ✅ Email automation
- ✅ WhatsApp integration
- ✅ Calendar synchronization
- ✅ PDF generation
- ✅ Responsive design
- ✅ Form validation
- ✅ Multi-language support

## 🤝 Built By

**Tera Works** - Professional Web Solutions
- WhatsApp: +94 71 576 8552
- Specializing in custom web applications for businesses

## 📄 License

This project is available for purchase and customization. Contact Tera Works for licensing and implementation details.

## 🌟 Why Choose This System?

### For Photographers:
- **Save Time**: Reduce booking management time by 80%
- **Professional Image**: Impress clients with automated, professional communication
- **Reduce Errors**: Eliminate manual booking errors and miscommunication
- **Focus on Photography**: Spend more time on your craft, less on administration
- **Increase Bookings**: Streamlined process leads to higher conversion rates

### Technical Benefits:
- **Fully Responsive**: Works perfectly on all devices
- **SEO Optimized**: Built with modern web standards
- **Fast Loading**: Optimized for performance
- **Secure**: Built with security best practices
- **Scalable**: Easy to extend and customize

## 📞 Get This System

Interested in implementing this booking system for your photography business?

**Contact Tera Works:**
- WhatsApp: [+94 71 576 8552](https://wa.me/***********)
- We provide complete setup, customization, and ongoing support

## 🚀 Deployment

This project can be deployed to any static hosting service:

### Recommended Platforms:
- **Vercel**: Professional hosting with automatic deployments
- **Netlify**: Fast and reliable static hosting
- **Firebase Hosting**: Google's hosting platform
- **Custom Domain**: Professional domain setup available

### Build for Production:
```bash
npm run build
```

The build files will be in the `dist` folder, ready for deployment.
