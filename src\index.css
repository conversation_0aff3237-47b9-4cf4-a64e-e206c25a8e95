
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 8%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 8%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 8%;

    --primary: 0 0% 8%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 8%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 8%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89%;
    --input: 0 0% 89%;
    --ring: 0 0% 8%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 0 0% 26%;
    --sidebar-primary: 0 0% 8%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 0 0% 96%;
    --sidebar-accent-foreground: 0 0% 8%;
    --sidebar-border: 0 0% 91%;
    --sidebar-ring: 0 0% 8%;
  }

  .dark {
    --background: 0 0% 8%;
    --foreground: 0 0% 98%;

    --card: 0 0% 8%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 8%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 8%;

    --secondary: 0 0% 17%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 17%;
    --muted-foreground: 0 0% 65%;

    --accent: 0 0% 17%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 17%;
    --input: 0 0% 17%;
    --ring: 0 0% 83%;
    --sidebar-background: 0 0% 8%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 0 0% 8%;
    --sidebar-accent: 0 0% 16%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 0 0% 16%;
    --sidebar-ring: 0 0% 83%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-black to-gray-600 bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply bg-white/95 backdrop-blur-md border border-gray-200/50;
  }

  .premium-shadow {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05);
  }

  .luxury-gradient {
    @apply bg-gradient-to-br from-black via-gray-900 to-gray-800;
  }

  .elegant-border {
    @apply border border-gray-200/30 shadow-lg;
  }
}

/* Mobile-specific enhancements */
@layer components {
  /* Enhanced mobile touch targets */
  @media (max-width: 768px) {
    .mobile-touch-target {
      min-height: 44px;
      min-width: 44px;
    }

    /* Better mobile form spacing */
    .mobile-form-spacing {
      @apply space-y-4;
    }

    /* Mobile-optimized cards */
    .mobile-card {
      @apply mx-2 rounded-lg;
    }

    /* Mobile-friendly text sizing */
    .mobile-text-responsive {
      font-size: clamp(0.875rem, 2.5vw, 1rem);
      line-height: 1.5;
    }

    /* Mobile input improvements */
    .mobile-input {
      @apply text-base; /* Prevents zoom on iOS */
      min-height: 44px;
    }

    /* Mobile button improvements */
    .mobile-button {
      @apply min-h-[44px] px-4 py-3 text-base;
    }

    /* Mobile-specific animations */
    .mobile-no-hover:hover {
      transform: none !important;
      scale: 1 !important;
    }
  }

  /* Extra small screens */
  @media (max-width: 480px) {
    .xs-padding {
      @apply px-3 py-2;
    }

    .xs-text {
      @apply text-sm;
    }

    .xs-spacing {
      @apply space-y-3;
    }
  }
}
