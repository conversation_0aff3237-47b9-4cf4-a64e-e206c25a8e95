# EmailJS Configuration Guide for Demo Booking System

## 🚨 Current Issue
Emails are being sent to the original photographer's email instead of the demo email (<EMAIL>).

## 🔧 Solution Steps

### Step 1: Verify EmailJS Account Configuration

1. **Login to EmailJS Dashboard**: https://dashboard.emailjs.com/
2. **Check Service Configuration**:
   - Service ID: `service_lk610td`
   - Ensure the service is connected to the correct email account
   - Verify the service is active and properly configured

### Step 2: Update Email Template Configuration

1. **Go to Email Templates**: https://dashboard.emailjs.com/admin/templates
2. **Find Template**: `template_7cm6mm7`
3. **Update Template Settings**:
   ```
   To Email: <EMAIL>
   From Name: Booking Demo System
   Subject: 🎬 DEMO - New Photography Booking Inquiry - {{client_name}}
   ```

### Step 3: Verify Template Variables

Ensure your template includes these variables:
```
{{client_name}}
{{client_email}}
{{client_phone}}
{{event_type}}
{{event_date}}
{{venue_location}}
{{booking_reference}}
{{submission_time}}
{{package_name}}
{{package_price}}
{{special_requests}}
```

### Step 4: Update Public Key (if needed)

If you're using a different EmailJS account, update the public key in `src/pages/Booking.tsx`:

```javascript
await emailjs.send(
  'service_lk610td',
  'template_7cm6mm7',
  emailData,
  'YOUR_NEW_PUBLIC_KEY_HERE'  // Replace with your actual public key
);
```

### Step 5: Test Configuration

1. **Test from EmailJS Dashboard**:
   - Go to your template
   - Click "Test it"
   - Send a test email to verify it <NAME_EMAIL>

2. **Test from Demo System**:
   - Fill out the booking form
   - Submit and check if email <NAME_EMAIL>

## 🔍 Debugging Steps

### Check Browser Console
Open browser developer tools and look for:
- EmailJS success/error messages
- Network requests to EmailJS API
- Any JavaScript errors

### Verify Email Data
The system logs email data to console. Check that:
- All required fields are populated
- Boolean flags are set correctly
- Email addresses are correct

## 📧 Current EmailJS Configuration

```javascript
Service ID: service_lk610td
Template ID: template_7cm6mm7
Public Key: htHcoxPC-A-mGS62O
Target Email: <EMAIL> (should be configured in EmailJS template)
```

## 🚨 Common Issues & Solutions

### Issue 1: Wrong Email Destination
**Problem**: Emails go to wrong address
**Solution**: Update the "To Email" field in EmailJS template settings

### Issue 2: Template Not Found
**Problem**: EmailJS returns template not found error
**Solution**: Verify template ID `template_7cm6mm7` exists in your account

### Issue 3: Service Not Found
**Problem**: EmailJS returns service not found error
**Solution**: Verify service ID `service_lk610td` exists and is active

### Issue 4: Authentication Error
**Problem**: EmailJS returns authentication error
**Solution**: Update public key with correct value from your account

## 📝 Quick Fix Checklist

- [ ] EmailJS account has service `service_lk610td`
- [ ] Service is connected to correct email provider
- [ ] Template `template_7cm6mm7` exists
- [ ] Template "To Email" is set to `<EMAIL>`
- [ ] Template includes all required variables
- [ ] Public key is correct for your account
- [ ] Test email from EmailJS dashboard works
- [ ] Browser console shows no errors

## 🎯 Expected Behavior

When the demo booking system works correctly:
1. User fills out booking form
2. Clicks "Submit via WhatsApp"
3. Email is sent to `<EMAIL>` with booking details
4. WhatsApp message is generated with booking information
5. User is redirected to success page

## 📞 Support

If issues persist:
- Check EmailJS documentation: https://www.emailjs.com/docs/
- Verify all configuration steps above
- Test with a simple template first
- Contact EmailJS support if needed

---

**Built by Tera Works** - Professional Web Solutions
WhatsApp: +94 71 576 8552
