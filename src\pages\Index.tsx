
import { motion } from 'framer-motion';
import { <PERSON>, Heart, Star, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';

const Index = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 via-white to-amber-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-rose-900 via-rose-800 to-amber-900 min-h-screen flex items-center">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="mb-6"
            >
              <Camera className="h-16 w-16 text-white mx-auto mb-4" />
            </motion.div>
            
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
              BOOKING DEMO
              <span className="block text-3xl md:text-4xl font-light text-rose-200 mt-2">
                PHOTOGRAPHY SYSTEM
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-rose-100 mb-12 leading-relaxed">
              Professional photography booking system demo - Streamline your business with automated bookings, email integration, and client management
            </p>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Button
                onClick={() => navigate('/booking')}
                size="lg"
                className="bg-gradient-to-r from-rose-500 to-amber-500 hover:from-rose-600 hover:to-amber-600 text-white font-semibold px-8 py-4 text-lg shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                <Calendar className="mr-2 h-5 w-5" />
                Book Your Session
              </Button>
              
              <Button
                onClick={() => window.open('https://wa.me/***********', '_blank')}
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-rose-800 font-semibold px-8 py-4 text-lg backdrop-blur-sm"
              >
                Contact Developer
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Services Section */}
      <div className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-rose-600 to-amber-600 bg-clip-text text-transparent mb-4">
              Our Services
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              From intimate engagement sessions to grand wedding celebrations, we capture every moment with perfection
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                icon: Heart,
                title: "Engagement Sessions",
                description: "Intimate and romantic sessions to celebrate your love story",
                gradient: "from-rose-500 to-pink-500"
              },
              {
                icon: Camera,
                title: "Wedding Photography",
                description: "Complete wedding day coverage with artistic storytelling",
                gradient: "from-amber-500 to-orange-500"
              },
              {
                icon: Star,
                title: "Homecoming Celebrations",
                description: "Traditional ceremonies captured with cultural sensitivity",
                gradient: "from-rose-600 to-amber-600"
              }
            ].map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.6 }}
                whileHover={{ y: -10 }}
              >
                <Card className="h-full border-0 shadow-xl bg-white/80 backdrop-blur-sm overflow-hidden group">
                  <CardContent className="p-8 text-center">
                    <div className={`w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-r ${service.gradient} flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300`}>
                      <service.icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 mb-4">{service.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{service.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 bg-gradient-to-r from-rose-900 via-rose-800 to-amber-900">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Start Your Journey?
            </h2>
            <p className="text-xl text-rose-100 mb-8 leading-relaxed">
              Let's create beautiful memories together. Book your consultation today and let's discuss how we can make your special day unforgettable.
            </p>
            <Button
              onClick={() => navigate('/booking')}
              size="lg"
              className="bg-white text-rose-800 hover:bg-rose-50 font-semibold px-12 py-4 text-lg shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              <Calendar className="mr-2 h-5 w-5" />
              Start Your Booking
            </Button>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Index;
