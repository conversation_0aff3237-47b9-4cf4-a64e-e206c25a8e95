import React from 'react';
import { CheckCircle, Home, Calendar, Mail, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const BookingSuccess = () => {
  const handleGoHome = () => {
    window.open('https://wa.me/94715768552', '_blank');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex flex-col">
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="max-w-2xl w-full">
        {/* Success Animation Container */}
        <div className="text-center mb-8">
          <div className="relative inline-block">
            <div className="absolute inset-0 bg-green-100 rounded-full animate-ping opacity-75"></div>
            <div className="relative bg-green-500 rounded-full p-6">
              <CheckCircle className="w-16 h-16 text-white" />
            </div>
          </div>
        </div>

        {/* Main Success Card */}
        <Card className="border-0 shadow-2xl bg-white">
          <CardContent className="p-8 text-center">
            {/* Header */}
            <div className="mb-6">
              <h1 className="text-3xl font-bold text-gray-800 mb-2">
                Demo Booking Submitted Successfully!
              </h1>
              <p className="text-lg text-gray-600">
                Thank you for testing the Booking Demo system
              </p>
            </div>

            {/* Success Message */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div className="text-left">
                  <h3 className="font-semibold text-green-800 mb-2">
                    Your inquiry has been received
                  </h3>
                  <p className="text-green-700 text-sm leading-relaxed">
                    This is a demo booking system. Your inquiry has been processed to demonstrate
                    the system's functionality. In a real implementation, this would be sent to
                    the photographer's email and WhatsApp for immediate follow-up.
                  </p>
                </div>
              </div>
            </div>

            {/* Demo Features Section */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Demo Features Demonstrated</h2>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="bg-blue-500 rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-3">
                    <Mail className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="font-medium text-gray-800 mb-1">Email Integration</h3>
                  <p className="text-sm text-gray-600">Professional email templates with booking details</p>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="bg-purple-500 rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-3">
                    <Phone className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="font-medium text-gray-800 mb-1">WhatsApp Integration</h3>
                  <p className="text-sm text-gray-600">Automated WhatsApp messaging for instant communication</p>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <div className="bg-green-500 rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-3">
                    <Calendar className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="font-medium text-gray-800 mb-1">Calendar Integration</h3>
                  <p className="text-sm text-gray-600">Automatic calendar invites for all events</p>
                </div>
              </div>
            </div>

            {/* Demo Information */}
            <div className="bg-gray-50 rounded-lg p-6 mb-8">
              <h3 className="font-semibold text-gray-800 mb-3">Interested in this booking system?</h3>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Phone className="w-4 h-4" />
                  <span className="text-sm">+94 71 576 8552 (Tera Works)</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('https://wa.me/94715768552', '_blank')}
                  className="border-green-300 text-green-700 hover:bg-green-50 transition-colors duration-200"
                >
                  Contact Developer
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleGoHome}
                className="bg-black hover:bg-gray-800 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <Home className="w-4 h-4" />
                <span>Contact Developer</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => window.open('https://wa.me/94715768552', '_blank')}
                className="border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-3 rounded-lg font-medium transition-colors duration-200"
              >
                Get This System
              </Button>
            </div>

            {/* Footer Note */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                This is a demonstration of a professional photography booking system.
                All features shown are fully functional and ready for implementation.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Demo Value Proposition Card */}
        <Card className="mt-6 border-0 shadow-lg bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-6 text-center">
            <h3 className="font-semibold text-gray-800 mb-2">Why Choose This Booking System?</h3>
            <p className="text-sm text-gray-600 mb-4">
              Streamline your photography business with professional automation
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="font-medium text-gray-800">Email Automation</div>
                <div className="text-gray-600">Professional templates</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-gray-800">WhatsApp Integration</div>
                <div className="text-gray-600">Instant communication</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-gray-800">Calendar Sync</div>
                <div className="text-gray-600">Automated scheduling</div>
              </div>
            </div>
          </CardContent>
        </Card>
        </div>
      </div>

      {/* Developer Credit Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 py-4">
        <div className="container mx-auto px-4 text-center">
          <p className="text-xs text-gray-500">
            Built by{' '}
            <a
              href="https://wa.me/94715768552"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-600 hover:text-gray-800 transition-colors duration-200"
            >
              Tera Works
            </a>
            {' '}• Professional Photography Booking System Demo
          </p>
        </div>
      </footer>
    </div>
  );
};

export default BookingSuccess;
